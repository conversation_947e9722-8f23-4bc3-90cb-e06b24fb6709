import { get, post, put, _delete } from '@/utils/request.js'

/**
 * 新增预约
 * @param data 表单数据
 */
export function addReservation(data) {
  return post('/houseKeeping/reservation',data);
}

/**
 * 修改预约管理
 * @param data
 */
export function updateReservation(data) {
  return put( '/houseKeeping/reservation', data );
}


/**
 * 查询预约管理列表
 * @param query 查询参数
 */
export function listReservation(query) {
  return get('/houseKeeping/reservation/list', query);
}



/**
 * 取消预约
 * @param data 包含reservationId和cancelReason的对象
 */
export function cancelReservation(data) {
  return post('/houseKeeping/reservation/cancel', data);
}


/**
 * 获取支付链接
 * @param reservationId
 * @returns {Promise<axios.AxiosResponse<any>>}
 * @constructor
 */
export function GetPayUrl(reservationId) {
  return get('/houseKeeping/hkOrder/pay', { reservationId })
}


