package org.dromara.houseKeeping.controller;

import java.util.List;

import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.houseKeeping.domain.bo.HkOrderBo;
import org.dromara.houseKeeping.domain.query.HkOrderQuery;
import org.dromara.houseKeeping.domain.vo.HkOrderVo;
import org.dromara.houseKeeping.service.IHkOrderService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 支付订单
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/houseKeeping/hkOrder")
public class HkOrderController extends BaseController {

    @Autowired
    private IHkOrderService hkOrderService;

    /**
     * 查询支付订单列表
     */
    @SaCheckPermission("houseKeeping:hkOrder:query")
    @GetMapping("/list")
    public TableDataInfo<HkOrderVo> list(HkOrderQuery query) {
        return hkOrderService.queryPageList(query);
    }

    /**
     * 导出支付订单列表
     */
    @SaCheckPermission("houseKeeping:hkOrder:export")
    @Log(title = "支付订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HkOrderQuery query, HttpServletResponse response) {
        List<HkOrderVo> list = hkOrderService.queryList(query);
        ExcelUtil.exportExcel(list, "支付订单", HkOrderVo.class, response);
    }

    /**
     * 获取支付订单详细信息
     *
     * @param orderId 主键
     */
    @SaCheckPermission(value = {"houseKeeping:hkOrder:query", "houseKeeping:hkOrder:edit"}, mode = SaMode.OR)
    @GetMapping("/{orderId}")
    public R<HkOrderVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable String orderId) {
        return R.ok(hkOrderService.queryById(orderId));
    }

    /**
     * 新增支付订单
     */
    @SaCheckPermission("houseKeeping:hkOrder:add")
    @Log(title = "支付订单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody HkOrderBo bo) {
        return toAjax(hkOrderService.insertByBo(bo));
    }

    /**
     * 修改支付订单
     */
    @SaCheckPermission("houseKeeping:hkOrder:edit")
    @Log(title = "支付订单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody HkOrderBo bo) {
        return toAjax(hkOrderService.updateByBo(bo));
    }

    /**
     * 删除支付订单
     *
     * @param orderIds 主键串
     */
    @SaCheckPermission("houseKeeping:hkOrder:remove")
    @Log(title = "支付订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable String[] orderIds) {
        return toAjax(hkOrderService.deleteWithValidByIds(List.of(orderIds)));
    }

    @SaCheckPermission("houseKeeping:hkOrder:add")
    @Log(title = "支付订单", businessType = BusinessType.UPDATE)
    @GetMapping("/pay/{reservationId}")
    public R<JSONObject> pay(@NotNull(message = "预约编号不能为空") @PathVariable String reservationId) {
        return this.hkOrderService.pay(reservationId);
    }

    /**
     * Mapay支付回调通知接口
     *
     * @param pid 商户ID
     * @param trade_no 支付平台订单号
     * @param out_trade_no 商户订单号
     * @param type 支付方式
     * @param name 商品名称
     * @param money 支付金额
     * @param trade_status 支付状态
     * @param sign 签名
     * @param sign_type 签名类型
     * @return 处理结果
     */
    @PostMapping("/notify")
    public String notify(@RequestParam String pid,
                        @RequestParam String trade_no,
                        @RequestParam String out_trade_no,
                        @RequestParam String type,
                        @RequestParam String name,
                        @RequestParam String money,
                        @RequestParam String trade_status,
                        @RequestParam String sign,
                        @RequestParam String sign_type) {

        log.info("收到Mapay支付回调通知：订单号={}, 支付状态={}, 金额={}", out_trade_no, trade_status, money);

        try {
            // 处理支付回调
            boolean success = hkOrderService.handlePaymentNotify(pid, trade_no, out_trade_no,
                type, name, money, trade_status, sign, sign_type);

            if (success) {
                log.info("支付回调处理成功：订单号={}", out_trade_no);
                return "success";
            } else {
                log.error("支付回调处理失败：订单号={}", out_trade_no);
                return "fail";
            }
        } catch (Exception e) {
            log.error("支付回调处理异常：订单号={}, 错误信息={}", out_trade_no, e.getMessage(), e);
            return "fail";
        }
    }

    /**
     * 测试Mapay配置
     */
    @GetMapping("/test-config")
    public R<String> testConfig() {
        try {
            if (hkOrderService instanceof HkOrderServiceImpl) {
                ((HkOrderServiceImpl) hkOrderService).testMapayConfig();
                return R.ok("配置测试完成，请查看日志");
            }
            return R.fail("无法测试配置");
        } catch (Exception e) {
            log.error("测试配置异常", e);
            return R.fail("测试配置异常：" + e.getMessage());
        }
    }

}
