package org.dromara.houseKeeping.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.common.mybatis.core.page.SortQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.dromara.houseKeeping.domain.Reservation;
import org.dromara.houseKeeping.domain.bo.ReservationBo;
import org.dromara.houseKeeping.domain.query.ReservationQuery;
import org.dromara.houseKeeping.domain.vo.ReservationVo;
import org.dromara.houseKeeping.mapper.ReservationMapper;
import org.dromara.houseKeeping.service.IReservationService;

import java.util.Collection;
import java.util.List;

/**
 * 预约管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-27
 */
@Service
public class ReservationServiceImpl extends ServiceImpl<ReservationMapper, Reservation> implements IReservationService {

    @Autowired
    private ReservationMapper reservationMapper;


    /**
     * 查询预约管理
     *
     * @param reservationId 主键
     * @return ReservationVo
     */
    @Override
    public ReservationVo queryById(String reservationId) {
        return reservationMapper.selectReservationById(reservationId);
    }

    /**
     * 分页查询预约管理列表
     *
     * @param query 查询对象
     * @return 预约管理分页列表
     */
    @Override
    public TableDataInfo<ReservationVo> queryPageList(ReservationQuery query) {
        return PageQuery.of(() -> reservationMapper.queryList(query));
    }

    /**
     * 查询预约管理列表
     *
     * @param query 查询对象
     * @return 预约管理列表
     */
    @Override
    public List<ReservationVo> queryList(ReservationQuery query) {
        return SortQuery.of(() -> reservationMapper.queryList(query));
    }

    /**
     * 新增预约管理
     *
     * @param bo 预约管理新增业务对象
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(ReservationBo bo) {
        Reservation add = MapstructUtils.convert(bo, Reservation.class);
        return save(add);
    }

    /**
     * 修改预约管理
     *
     * @param bo 预约管理编辑业务对象
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(ReservationBo bo) {
        Reservation update = MapstructUtils.convert(bo, Reservation.class);
        return updateById(update);
    }

    /**
     * 批量删除预约管理信息
     *
     * @param ids 待删除的主键集合
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<String> ids) {
        return removeByIds(ids);
    }


    /**
     * 取消预约
     * @param reservationId
     * @return
     */
    @Override
    public int cancelReservation(String reservationId, String cancelReason) {
        String userId = LoginHelper.getUserIdStr();
        if(StrUtil.hasEmpty(userId)){
            throw new ServiceException("请先登录在操作");
        }
        LambdaQueryWrapper<Reservation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Reservation::getReservationId, reservationId);
        Reservation reservation = this.reservationMapper.selectOne(wrapper);
        if(reservation == null){
            throw new ServiceException("无效的预约编号");
        }
        // 执行更新操作
        reservation.setStatus("已取消");
        reservation.setCancelReason(cancelReason);
        return this.reservationMapper.updateById(reservation);
    }

    /**
     * 根据预约ID更新预约状态
     *
     * @param reservationId 预约ID
     * @param status 新状态
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatusByReservationId(String reservationId, String status) {
        try {
            System.out.println("开始更新预约状态：预约ID=" + reservationId + ", 新状态=" + status);

            LambdaQueryWrapper<Reservation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Reservation::getReservationId, reservationId);
            Reservation reservation = this.reservationMapper.selectOne(wrapper);

            if (reservation == null) {
                System.out.println("ERROR: 预约不存在：预约ID=" + reservationId);
                return false;
            }

            System.out.println("当前预约状态：" + reservation.getStatus() + " -> 新状态：" + status);

            reservation.setStatus(status);
            int result = this.reservationMapper.updateById(reservation);

            boolean success = result > 0;
            System.out.println("预约状态更新结果：" + (success ? "成功" : "失败"));

            return success;
        } catch (Exception e) {
            System.out.println("ERROR: 更新预约状态异常：" + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
}
