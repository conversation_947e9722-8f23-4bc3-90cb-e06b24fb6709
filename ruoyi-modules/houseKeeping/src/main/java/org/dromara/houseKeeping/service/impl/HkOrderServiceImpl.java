package org.dromara.houseKeeping.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.common.mybatis.core.page.SortQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.houseKeeping.Configuration.MapayConfiguration;
import org.dromara.houseKeeping.domain.vo.ReservationVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.dromara.houseKeeping.domain.HkOrder;
import org.dromara.houseKeeping.domain.bo.HkOrderBo;
import org.dromara.houseKeeping.domain.query.HkOrderQuery;
import org.dromara.houseKeeping.domain.vo.HkOrderVo;
import org.dromara.houseKeeping.mapper.HkOrderMapper;
import org.dromara.houseKeeping.service.IHkOrderService;

import java.util.Collection;
import java.util.List;

/**
 * 支付订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Service
public class HkOrderServiceImpl extends ServiceImpl<HkOrderMapper, HkOrder> implements IHkOrderService {

    @Autowired
    private HkOrderMapper hkOrderMapper;

    @Autowired
    private MapayConfiguration mapayConfig;

    @Autowired
    private ReservationServiceImpl reservationService;


    /**
     * 查询支付订单
     *
     * @param orderId 主键
     * @return HkOrderVo
     */
    @Override
    public HkOrderVo queryById(String orderId) {
        return hkOrderMapper.selectVoHkOrderById(orderId);
    }

    /**
     * 分页查询支付订单列表
     *
     * @param query 查询对象
     * @return 支付订单分页列表
     */
    @Override
    public TableDataInfo<HkOrderVo> queryPageList(HkOrderQuery query) {
        return PageQuery.of(() -> baseMapper.queryList(query));
    }

    /**
     * 查询支付订单列表
     *
     * @param query 查询对象
     * @return 支付订单列表
     */
    @Override
    public List<HkOrderVo> queryList(HkOrderQuery query) {
        return SortQuery.of(() -> baseMapper.queryList(query));
    }

    /**
     * 新增支付订单
     *
     * @param bo 支付订单新增业务对象
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(HkOrderBo bo) {
        HkOrder add = MapstructUtils.convert(bo, HkOrder.class);
        return save(add);
    }

    /**
     * 修改支付订单
     *
     * @param bo 支付订单编辑业务对象
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(HkOrderBo bo) {
        HkOrder update = MapstructUtils.convert(bo, HkOrder.class);
        return updateById(update);
    }

    /**
     * 批量删除支付订单信息
     *
     * @param ids 待删除的主键集合
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<String> ids) {
        return removeByIds(ids);
    }

    @Override
    public R<JSONObject> pay(String reservationId) {
        String userId = LoginHelper.getUserIdStr();
        if(StrUtil.hasEmpty(userId)){
            throw new ServiceException("请先登录！");
        }
        ReservationVo reservationVo = this.reservationService.queryById(reservationId);
        // 判断预约信息是否存在
        if(reservationVo == null){
            throw new ServiceException("预约信息不存在！");
        }
        // 判断订单是否属于该用户
        if(!reservationVo.getCustomerId().equals(userId)){
            throw new ServiceException("您没有权限支付该订单！");
        }

        // 构建订单
        HkOrder hkOrder = new HkOrder();
        hkOrder.setReservationId(reservationId);
        hkOrder.setCustomerId(userId);
        hkOrder.setServeId(reservationVo.getServeId());
        hkOrder.setStaffId(reservationVo.getStaffId());
        hkOrder.setOrderAmount(reservationVo.getServe().getPrice());

        // 保存订单
        boolean save = this.save(hkOrder);

        if(!save){
            throw new ServiceException("请重试！");
        }

        // 将这条记录从数据中重新查出来
        LambdaQueryWrapper<HkOrder> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HkOrder::getReservationId,reservationId);
        HkOrder hkOrder1 = this.hkOrderMapper.selectOne(lqw);

        // 获取支付二维码


        return null;
    }

    public JSONObject genQrCode(String orderId,ReservationVo reservationVo){
        String payUrl = mapayConfig.getBaseUrl() + mapayConfig.getPid() + "&type=" + mapayConfig.getType() + "&out_trade_no" + orderId
            + "&notify_url=" + mapayConfig.getNotifyUrl() +





        return null;
    }
}
