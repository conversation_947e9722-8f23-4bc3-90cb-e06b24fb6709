package org.dromara.houseKeeping.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.common.mybatis.core.page.SortQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.houseKeeping.Configuration.MapayConfiguration;
import org.dromara.houseKeeping.domain.vo.ReservationVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.dromara.houseKeeping.domain.HkOrder;
import org.dromara.houseKeeping.domain.bo.HkOrderBo;
import org.dromara.houseKeeping.domain.query.HkOrderQuery;
import org.dromara.houseKeeping.domain.vo.HkOrderVo;
import org.dromara.houseKeeping.mapper.HkOrderMapper;
import org.dromara.houseKeeping.service.IHkOrderService;

import java.util.Collection;
import java.util.List;
import java.util.TreeMap;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 支付订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Service
public class HkOrderServiceImpl extends ServiceImpl<HkOrderMapper, HkOrder> implements IHkOrderService {

    @Autowired
    private HkOrderMapper hkOrderMapper;

    @Autowired
    private MapayConfiguration mapayConfig;

    @Autowired
    private ReservationServiceImpl reservationService;


    private final String  SITENAME = "打不过你编程网";

    /**
     * 查询支付订单
     *
     * @param orderId 主键
     * @return HkOrderVo
     */
    @Override
    public HkOrderVo queryById(String orderId) {
        return hkOrderMapper.selectVoHkOrderById(orderId);
    }

    /**
     * 分页查询支付订单列表
     *
     * @param query 查询对象
     * @return 支付订单分页列表
     */
    @Override
    public TableDataInfo<HkOrderVo> queryPageList(HkOrderQuery query) {
        return PageQuery.of(() -> baseMapper.queryList(query));
    }

    /**
     * 查询支付订单列表
     *
     * @param query 查询对象
     * @return 支付订单列表
     */
    @Override
    public List<HkOrderVo> queryList(HkOrderQuery query) {
        return SortQuery.of(() -> baseMapper.queryList(query));
    }

    /**
     * 新增支付订单
     *
     * @param bo 支付订单新增业务对象
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(HkOrderBo bo) {
        HkOrder add = MapstructUtils.convert(bo, HkOrder.class);
        return save(add);
    }

    /**
     * 修改支付订单
     *
     * @param bo 支付订单编辑业务对象
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(HkOrderBo bo) {
        HkOrder update = MapstructUtils.convert(bo, HkOrder.class);
        return updateById(update);
    }

    /**
     * 批量删除支付订单信息
     *
     * @param ids 待删除的主键集合
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<String> ids) {
        return removeByIds(ids);
    }

    @Override
    public R<JSONObject> pay(String reservationId) {
        try {
            log.info("开始处理支付请求，预约ID：{}", reservationId);

            String userId = LoginHelper.getUserIdStr();
            log.info("当前用户ID：{}", userId);

            if(StrUtil.hasEmpty(userId)){
                throw new ServiceException("请先登录！");
            }

        ReservationVo reservationVo = this.reservationService.queryById(reservationId);
        log.info("查询到的预约信息：{}", reservationVo);

        // 判断预约信息是否存在
        if(reservationVo == null){
            throw new ServiceException("预约信息不存在！");
        }

        // 检查服务信息是否存在
        if(reservationVo.getServe() == null){
            log.error("预约信息中的服务信息为空，预约ID：{}", reservationId);
            throw new ServiceException("服务信息不存在！");
        }

        log.info("服务信息：{}", reservationVo.getServe());

        // 判断订单是否属于该用户
        if(!reservationVo.getCustomerId().equals(userId)){
            throw new ServiceException("您没有权限支付该订单！");
        }

        // 构建订单
        HkOrder hkOrder = new HkOrder();
        hkOrder.setReservationId(reservationId);
        hkOrder.setCustomerId(userId);
        hkOrder.setServeId(reservationVo.getServeId());
        hkOrder.setStaffId(reservationVo.getStaffId());
        hkOrder.setOrderAmount(reservationVo.getServe().getPrice());

        log.info("构建的订单信息：{}", hkOrder);

        // 保存订单
        boolean save = this.save(hkOrder);
        log.info("订单保存结果：{}", save);

        if(!save){
            throw new ServiceException("请重试！");
        }

        // 将这条记录从数据中重新查出来
        LambdaQueryWrapper<HkOrder> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HkOrder::getReservationId,reservationId);
        HkOrder hkOrder1 = this.hkOrderMapper.selectOne(lqw);

        log.info("重新查询的订单信息：{}", hkOrder1);

        if(hkOrder1 == null) {
            throw new ServiceException("订单创建失败，请重试！");
        }

        // 验证配置
        if (mapayConfig == null) {
            log.error("Mapay配置未加载");
            throw new ServiceException("支付配置未加载，请联系管理员");
        }

        log.info("当前Mapay配置：pid={}, baseUrl={}, type={}",
            mapayConfig.getPid(), mapayConfig.getBaseUrl(), mapayConfig.getType());

        // 生成支付跳转链接
        String payUrl = generateMapayPayUrl(hkOrder1, reservationVo);

        log.info("生成支付链接成功，订单号：{}，支付链接：{}", hkOrder1.getOrderId(), payUrl);

        JSONObject result = new JSONObject();
        result.put("payUrl", payUrl);
        result.put("orderId", hkOrder1.getOrderId());
        result.put("orderAmount", hkOrder1.getOrderAmount());
        result.put("serviceName", reservationVo.getServe().getName());

        log.info("返回的结果数据：{}", result);

        return R.ok(result);

        } catch (ServiceException e) {
            log.error("支付处理业务异常：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("支付处理系统异常：", e);
            throw new ServiceException("支付处理失败：" + e.getMessage());
        }
    }

    /**
     * 生成Mapay支付跳转链接
     *
     * @param hkOrder 订单信息
     * @param reservationVo 预约信息
     * @return 支付跳转链接
     */
    private String generateMapayPayUrl(HkOrder hkOrder, ReservationVo reservationVo) {
        try {
            log.info("开始生成Mapay支付链接，订单号：{}", hkOrder.getOrderId());

            // 构建支付参数
            TreeMap<String, String> params = new TreeMap<>();
            params.put("pid", mapayConfig.getPid());
            params.put("type", mapayConfig.getType());
            params.put("out_trade_no", hkOrder.getOrderId());
            params.put("notify_url", mapayConfig.getNotifyUrl());
            params.put("return_url", mapayConfig.getReturnUrl());
            params.put("name", reservationVo.getServe().getName());
            params.put("money", hkOrder.getOrderAmount().toString());
            params.put("sitename", SITENAME);

            log.debug("支付参数：{}", params);

            // 生成签名
            String sign = generateSign(params);
            params.put("sign", sign);
            params.put("sign_type", "MD5");

            // 构建URL
            StringBuilder urlBuilder = new StringBuilder(mapayConfig.getBaseUrl());
            for (String key : params.keySet()) {
                urlBuilder.append(key).append("=")
                    .append(URLEncoder.encode(params.get(key), StandardCharsets.UTF_8))
                    .append("&");
            }

            // 移除最后一个&符号
            String payUrl = urlBuilder.toString();
            if (payUrl.endsWith("&")) {
                payUrl = payUrl.substring(0, payUrl.length() - 1);
            }

            return payUrl;

        } catch (Exception e) {
            throw new ServiceException("生成支付链接失败：" + e.getMessage());
        }
    }

    /**
     * 生成Mapay签名
     *
     * @param params 参数Map
     * @return 签名字符串
     */
    private String generateSign(TreeMap<String, String> params) {
        // 按照Mapay文档要求的签名算法
        // (money={商品金额}&name={商品名称}&notify_url={异步通知地址}&out_trade_no={商户订单号}&pid={商户ID}&return_url={同步通知地址}&sitename={站点名称}&type={支付方式}{商户密匙})

        StringBuilder signBuilder = new StringBuilder();
        signBuilder.append("money=").append(params.get("money"))
                   .append("&name=").append(params.get("name"))
                   .append("&notify_url=").append(params.get("notify_url"))
                   .append("&out_trade_no=").append(params.get("out_trade_no"))
                   .append("&pid=").append(params.get("pid"))
                   .append("&return_url=").append(params.get("return_url"))
                   .append("&sitename=").append(params.get("sitename"))
                   .append("&type=").append(params.get("type"))
                   .append(mapayConfig.getKey());

        String signString = signBuilder.toString();
        log.debug("签名原文：{}", signString);

        // 使用MD5加密
        String sign = DigestUtil.md5Hex(signString);
        log.debug("生成签名：{}", sign);

        return sign;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handlePaymentNotify(String pid, String trade_no, String out_trade_no,
                                      String type, String name, String money,
                                      String trade_status, String sign, String sign_type) {

        log.info("处理支付回调通知：订单号={}, 支付状态={}, 金额={}", out_trade_no, trade_status, money);

        try {
            // 1. 验证签名
            if (!verifyNotifySign(pid, trade_no, out_trade_no, type, name, money, trade_status, sign)) {
                log.error("支付回调签名验证失败：订单号={}", out_trade_no);
                return false;
            }

            // 2. 验证商户ID
            if (!mapayConfig.getPid().equals(pid)) {
                log.error("支付回调商户ID不匹配：期望={}, 实际={}", mapayConfig.getPid(), pid);
                return false;
            }

            // 3. 查询订单
            HkOrder order = getById(out_trade_no);
            if (order == null) {
                log.error("支付回调订单不存在：订单号={}", out_trade_no);
                return false;
            }

            // 4. 检查订单状态，避免重复处理
            if ("1".equals(order.getStatus())) {
                log.info("订单已支付，无需重复处理：订单号={}", out_trade_no);
                return true;
            }

            // 5. 验证金额
            if (!order.getOrderAmount().toString().equals(money)) {
                log.error("支付回调金额不匹配：订单金额={}, 回调金额={}", order.getOrderAmount(), money);
                return false;
            }

            // 6. 处理支付成功
            if ("TRADE_SUCCESS".equals(trade_status)) {
                order.setStatus("1"); // 设置为已支付
                boolean updated = updateById(order);

                if (updated) {
                    log.info("订单支付成功处理完成：订单号={}, 金额={}", out_trade_no, money);
                    return true;
                } else {
                    log.error("更新订单状态失败：订单号={}", out_trade_no);
                    return false;
                }
            } else {
                log.warn("支付状态不是成功：订单号={}, 状态={}", out_trade_no, trade_status);
                return false;
            }

        } catch (Exception e) {
            log.error("处理支付回调异常：订单号={}, 错误信息={}", out_trade_no, e.getMessage(), e);
            throw new ServiceException("处理支付回调失败：" + e.getMessage());
        }
    }

    /**
     * 验证支付回调签名
     */
    private boolean verifyNotifySign(String pid, String trade_no, String out_trade_no,
                                   String type, String name, String money,
                                   String trade_status, String sign) {

        // 按照Mapay文档要求构建签名字符串
        // MD5(money={支付金额}&name=测试商品&out_trade_no={商户订单号}&pid={商户ID}&trade_no={支付平台订单号}&trade_status=TRADE_SUCCESS&type={支付方式})
        StringBuilder signBuilder = new StringBuilder();
        signBuilder.append("money=").append(money)
                   .append("&name=").append(name)
                   .append("&out_trade_no=").append(out_trade_no)
                   .append("&pid=").append(pid)
                   .append("&trade_no=").append(trade_no)
                   .append("&trade_status=").append(trade_status)
                   .append("&type=").append(type);

        String signString = signBuilder.toString();
        String expectedSign = DigestUtil.md5Hex(signString);

        log.debug("回调签名验证：原文={}, 期望签名={}, 实际签名={}", signString, expectedSign, sign);

        return expectedSign.equals(sign);
    }

    /**
     * 测试配置是否正确加载
     */
    public void testMapayConfig() {
        log.info("=== 测试Mapay配置 ===");
        log.info("配置对象：{}", mapayConfig);
        if (mapayConfig != null) {
            log.info("PID：{}", mapayConfig.getPid());
            log.info("Key：{}", mapayConfig.getKey() != null ? "已配置" : "未配置");
            log.info("BaseUrl：{}", mapayConfig.getBaseUrl());
            log.info("NotifyUrl：{}", mapayConfig.getNotifyUrl());
            log.info("ReturnUrl：{}", mapayConfig.getReturnUrl());
            log.info("Type：{}", mapayConfig.getType());
        } else {
            log.error("Mapay配置为null");
        }
        log.info("=== 配置测试完成 ===");
    }

}
