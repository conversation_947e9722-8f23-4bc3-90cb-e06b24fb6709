package org.dromara.houseKeeping.Configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Mapay支付配置类
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "mapay")
public class MapayConfiguration {

    /**
     * 商户号
     */
    private String pid;

    /**
     * 密钥
     */
    private String key;

    /**
     * 对接接口
     */
    private String baseUrl;

    /**
     * 回调地址
     */
    private String returnUrl;

    /**
     * 支付方式
     */
    private String type;

}
