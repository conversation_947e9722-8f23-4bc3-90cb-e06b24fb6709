package org.dromara.houseKeeping.Configuration;

import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Mapay支付配置类
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "mapay")
public class MapayConfiguration {

    /**
     * 商户号
     */
    private String pid;

    /**
     * 密钥
     */
    private String key;

    /**
     * 对接接口
     */
    private String baseUrl;

    /**
     * 回调地址
     */
    private String returnUrl;

    /**
     * 支付方式
     */
    private String type;

    /**
     * 配置加载完成后的初始化方法
     */
    @PostConstruct
    public void init() {
        log.info("=== Mapay支付配置类加载完成 ===");
        log.info("商户号: {}", maskSensitiveInfo(pid));
        log.info("密钥: {}", maskSensitiveInfo(key));
        log.info("接口地址: {}", baseUrl);
        log.info("回调地址: {}", returnUrl);
        log.info("支付方式: {}", type);
        log.info("=== Mapay支付配置初始化成功 ===");
    }

    /**
     * 脱敏处理敏感信息
     * @param info 敏感信息
     * @return 脱敏后的信息
     */
    private String maskSensitiveInfo(String info) {
        if (info == null || info.length() <= 4) {
            return "****";
        }
        return info.substring(0, 2) + "****" + info.substring(info.length() - 2);
    }

}
