package org.dromara.houseKeeping.Configuration;


import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Configuration
@Component
public class MapayConfiguration {

    /**
     * 商户号
     */
    private String  pid = "Configuration";

    /**
     * 密钥
     */
    private String key = "567n55L5Bnum5Bn8uT9TpBL7LqN55P76";


    /**
     * 对接接口
     */
    private String baseUrl = "https://api.xcypay.com/";


    /**
     * 回调地址
     */
    private String returnUrl = "http://localhost:8080/houseKeeping/hkOrder/notify";

    


}
